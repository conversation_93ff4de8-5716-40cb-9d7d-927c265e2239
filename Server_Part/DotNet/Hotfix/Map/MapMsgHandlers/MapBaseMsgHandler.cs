namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientMoveInHandler : MessageLocationHandler<MapNode, ClientMoveInMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientMoveInMsg message)
    {
      // 检查用户是否在当前地图
      LogicRet getUserRet = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user.SendToast(getUserRet.Message);
        return;
      }
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      MapNode targetMapNode = GlobalInfoCache.Instance.GetMapNode(message.targetMap, message.targetPoint);
      if (targetMapNode == null)
      {
        ETLog.Error($"目标地图节点为空: {message.targetMap} {message.targetPoint}");
        user.SendToast("目标地图不存在");
        return;
      }
      LogicRet logicRet = moveComponent.CanMove(targetMapNode);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      logicRet = moveComponent.CanReach(targetMapNode);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      logicRet = moveComponent.MoveTo(targetMapNode, true);
      if (!logicRet.IsSuccess)
      {
        ETLog.Error($"移动失败: {logicRet.Message}");
        user.SendToast(logicRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

@NeedLogin
  @LockReq
  @MapBizType(MapBizEnum.Fast_Transfer)
  public List<CommonOutParams> fastTransfer(FastTransferIn in) throws Exception
    {
      String userId = ThreadUtil.getUser().id;
      UserCheckUtil.notInLiveState(ThreadUtil.getUser(), LiveStateEnum.FIGHTING, LiveStateEnum.DEAD);
      UserCacheInfo userCacheInfo = ThreadUtil.getUserCacheInfo();
      User user = userCacheInfo.user;
      MapNode mapNode = mapProc.getMapNode(in.transMap, in.transPoint);
    if (mapNode == null) {
        throw new MyError(MapErrorEnum.MAP_NOT_EXIST);
      }
    if (canNotUseFastTransferMap.contains(user.nowMap)) {
        throw new MyWarn("当前无法使用快捷传送！");
      }
    if (user.vipAfkSystem != null) {
        throw new MyWarn("挂机中无法使用快捷传送！");
      }
      List<CommonOutParams> rets = new ArrayList<>();
    BagSystem bagSystem = userCacheInfo.bagSystem;
  Treasure yuMao = bagSystem.GetThingInBag(ThingNameEnum.JiYi_YuMao, null, null);
    if (yuMao == null) {
      throw new MyWarn("该地点不在保存地点中！");
}
boolean find = false;
List<PointInfo> allPoints = new ArrayList<>();
allPoints.addAll(yuMao.cityPoints);
allPoints.addAll(yuMao.outPoints);
for (PointInfo pointInfo : allPoints)
{
  if (pointInfo.mapName.equals(in.transMap) && pointInfo.pointName.equals(in.transPoint))
    find = true;
}
if (!find)
{
  throw new MyWarn("该地点不在保存地点中！");
}
if (!in.useFengZheng) {
      Treasure chuansong = bagSystem.GetThingInBag(ThingNameEnum.ChuanSong_Shi, null, null);
if (chuansong == null)
{
  throw new MyWarn("您的背包中没有传送石！");
}
if (mapNode.nodeType != MapNodeType.CITY)
{
  throw new MyWarn("传送地点不是城镇地区！");
}
if (chuansong.coolTime != null && chuansong.coolTime > System.currentTimeMillis())
{
  throw new MyWarn("传送石还在冷却中！");
}
mapNode = mapProc.changeUserPoint(ThreadUtil.getUser(), ThreadUtil.getMapNode(), mapNode);
LogUtil.LevelInfo(10, "fastTransfer", userId, in.useFengZheng, in.transMap, in.transPoint, mapNode.mapName,
    mapNode.pointName);
chuansong.coolTime = System.currentTimeMillis() + chuansong.cd;
UpdateBagThingsOut updateBagThingsOut = new UpdateBagThingsOut();
updateBagThingsOut.updateList.add(chuansong);
rets.add(updateBagThingsOut);
    } else
{
  Treasure fengzheng = bagSystem.GetThingInBag(in.fengZhengId);
  if (fengzheng == null || fengzheng.num < 1)
  {
    throw new MyWarn("背包中没有足够多的纸风筝！");
  }
  mapNode = mapProc.changeUserPoint(ThreadUtil.getUser(), ThreadUtil.getMapNode(), mapNode);
  LogUtil.LevelInfo(10, "fastTransfer", userId, in.useFengZheng, in.transMap, in.transPoint, mapNode.mapName,
      mapNode.pointName);
  bagSystem.addThingNumWithSend(fengzheng, -1);
  taskProc.fillTaskRequire(userId, TaskRequireType.Use_Thing_Cond, ThingNameEnum.Zhi_FengZheng.toString(), 1);
}
rets.add(new ShowToastOut("传送成功！"));
return rets;
  }

  @NeedLogin
  @LockReq
  @MapBizType(MapBizEnum.Travel_Map_With_Plane)
  public CommonOutParams travelMapWithPlane(TravelMapWithPlaneIn in) throws Exception
{
  User user = ThreadUtil.getUser();
  BagSystem bagSystem = ThreadUtil.getBagSystem();
  MapNode nowNode = ThreadUtil.getMapNode();
    if (nowNode.nodeType != MapNodeType.CITY || !nowNode.pointName.equals("驿站")) {
    throw new MyWarn("当前不在驿站");
  }
    if (nowNode.mapName.equals(in.targetMap)) {
    throw new MyWarn("当前地图与目标地图相同");
  }
  MapNode targetNode = mapProc.getMapNode(in.targetMap, "驿站");
    if (targetNode == null) {
    throw new MyWarn("该区域暂未开放");
  }
    if (user.nowAttack.level > 10) {
    if (bagSystem.bag.coin < 100)
    {
      throw new MyWarn("您的金币不足");
    }
    bagSystem.bag.coin -= 100;
    bagSystem.sendNewCoin();
  }
  mapProc.changeUserPoint(user, nowNode, targetNode);
  chatProc.sendMessageToMapUsers(
        new SendChatOut("专用空姐挽着<u>" + user.name + "</u>,登上波音气死妻,一路欢声笑语的往<u>" + targetNode.mapName + "</u>去了.",
            ChatType.Local_Chat),
        nowNode, user.id);
chatProc.sendMessageToMapUsers(
    new SendChatOut("来自<u>" + nowNode.mapName + "</u>的波音气死妻号飞机落地，专用空姐挽着<u>" + user.name + "</u>，载歌载舞地走了下来",
        ChatType.Local_Chat),
    targetNode);
return new ShowToastOut("搭乘飞机成功");
  }
}